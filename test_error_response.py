#!/usr/bin/env python
"""
Test script to verify error response format for invalid template_id.
"""

import os
import sys
import django
import requests

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# API base URL
BASE_URL = "http://localhost:8005/api/v1/invoice-generator"

def get_auth_token():
    """Get authentication token for the test user."""
    response = requests.post("http://localhost:8005/api/v1/users/token/", {
        "email": "<EMAIL>",
        "password": "testpass123"
    })
    
    if response.status_code == 200:
        token = response.json()["access"]
        return token
    else:
        return None

def test_error_response():
    """Test error response format for invalid template_id."""
    print("🧪 Testing Error Response Format")
    print("=" * 40)
    
    token = get_auth_token()
    if not token:
        print("✗ Authentication failed")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Use an existing company template but override with invalid template_id
    response = requests.get(f"{BASE_URL}/company-templates/", headers=headers)
    if response.status_code != 200:
        print("✗ Failed to get company templates")
        return False
    
    templates = response.json()["results"]
    if not templates:
        print("✗ No company templates found")
        return False
    
    company_template = templates[0]
    
    # Test with invalid template_id in request
    request_data = {
        "csv_data": [{"Invoice ID": "INV-001", "Client Company": "Test Client", "Line Amount": "100.00"}],
        "column_mappings": {"invoice_number": "Invoice ID", "client_company": "Client Company", "line_amount": "Line Amount"},
        "company_template_id": company_template['id'],
        "template_id": "invalid_template_name"  # This doesn't exist
    }
    
    response = requests.post(f"{BASE_URL}/generate-invoices/", 
                           json=request_data, headers=headers)
    
    print(f"Response status: {response.status_code}")
    
    if response.status_code == 404:
        result = response.json()
        print("✅ Correct 404 error returned!")
        print(f"Error message: {result.get('error', 'No error message')}")
        
        # Check if our enhanced error response includes available templates
        if 'available_templates' in result:
            print(f"✅ Available templates listed: {result['available_templates']}")
        else:
            print("⚠️ Available templates not included in response")
            
        if 'requested_template' in result:
            print(f"✅ Requested template shown: {result['requested_template']}")
        else:
            print("⚠️ Requested template not included in response")
            
        return True
    else:
        print(f"✗ Expected 404 error, got {response.status_code}")
        try:
            print(f"Response: {response.json()}")
        except:
            print(f"Response text: {response.text}")
        return False

if __name__ == "__main__":
    success = test_error_response()
    if success:
        print("\n✅ Error response format is correct!")
        print("Our improvements include:")
        print("  - Detailed error messages")
        print("  - List of available templates")
        print("  - Requested template name")
        print("  - Proper HTTP status codes")
    else:
        print("\n❌ Error response format test failed!")
        sys.exit(1)
