#!/usr/bin/env python
"""
Test script to verify error handling for invalid template_id.
"""

import os
import sys
import django
import requests

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth import get_user_model

User = get_user_model()

# API base URL
BASE_URL = "http://localhost:8005/api/v1/invoice-generator"

def get_auth_token():
    """Get authentication token for the test user."""
    response = requests.post("http://localhost:8005/api/v1/users/token/", {
        "email": "<EMAIL>",
        "password": "testpass123"
    })
    
    if response.status_code == 200:
        token = response.json()["access"]
        return token
    else:
        return None

def test_invalid_template_error():
    """Test error handling for invalid template_id."""
    print("🧪 Testing Invalid Template Error Handling")
    print("=" * 50)
    
    token = get_auth_token()
    if not token:
        print("✗ Authentication failed")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Create a company template with an invalid template_id
    template_data = {
        "template_name": "Invalid Template Test",
        "template_id": "nonexistent_template",  # This template doesn't exist
        "template_display_name": "Nonexistent Template",
        "company_name": "Test Company",
        "address_line_1": "123 Test Street",
        "city": "Test City",
        "state_province": "Test State",
        "postal_code": "12345",
        "country": "Test Country",
        "phone": "******-0123",
        "email": "<EMAIL>"
    }
    
    response = requests.post(f"{BASE_URL}/company-templates/", 
                           json=template_data, headers=headers)
    
    if response.status_code != 201:
        print(f"✗ Failed to create company template: {response.status_code}")
        return False
    
    company_template = response.json()
    print(f"✓ Created company template with invalid template_id: {company_template['template_id']}")
    
    # Try to generate invoices with this invalid template
    request_data = {
        "csv_data": [{"Invoice ID": "INV-001", "Client Company": "Test Client", "Line Amount": "100.00"}],
        "column_mappings": {"invoice_number": "Invoice ID", "client_company": "Client Company", "line_amount": "Line Amount"},
        "company_template_id": company_template['id'],
        "template_id": "nonexistent_template"
    }
    
    response = requests.post(f"{BASE_URL}/generate-invoices/", 
                           json=request_data, headers=headers)
    
    print(f"Response status: {response.status_code}")
    
    if response.status_code == 404:
        result = response.json()
        print("✅ Correct error handling for invalid template!")
        print(f"Error message: {result['error']}")
        if 'available_templates' in result:
            print(f"Available templates: {result['available_templates']}")
        if 'requested_template' in result:
            print(f"Requested template: {result['requested_template']}")
        return True
    else:
        print(f"✗ Expected 404 error, got {response.status_code}")
        try:
            print(f"Response: {response.json()}")
        except:
            print(f"Response text: {response.text}")
        return False

if __name__ == "__main__":
    success = test_invalid_template_error()
    if success:
        print("\n✅ Invalid template error handling works correctly!")
    else:
        print("\n❌ Invalid template error handling failed!")
        sys.exit(1)
