#!/usr/bin/env python
"""
Test script to verify the invoice generation fix for template_id issue.
"""

import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth import get_user_model
from apps.invoice_generator.models import CompanyTemplate

User = get_user_model()

# API base URL
BASE_URL = "http://localhost:8005/api/v1/invoice-generator"

def create_test_user():
    """Create a test user for API testing."""
    try:
        user = User.objects.get(email="<EMAIL>")
        print(f"Using existing test user: {user.email}")
    except User.DoesNotExist:
        user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Invoice",
            last_name="Test",
            is_active=True
        )
        print(f"Created test user: {user.email}")
    return user

def get_auth_token():
    """Get authentication token for the test user."""
    response = requests.post("http://localhost:8005/api/v1/users/token/", {
        "email": "<EMAIL>",
        "password": "testpass123"
    })
    
    if response.status_code == 200:
        token = response.json()["access"]
        print("✓ Authentication successful")
        return token
    else:
        print(f"✗ Authentication failed: {response.status_code}")
        return None

def test_invoice_generation():
    """Test the invoice generation with template_id fix."""
    print("🧪 Testing Invoice Generation Fix")
    print("=" * 50)
    
    # Step 1: Create test user and authenticate
    user = create_test_user()
    token = get_auth_token()
    if not token:
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Step 2: Create a company template with a valid template_id
    print("\n📋 Step 2: Creating Company Template")
    template_data = {
        "template_name": "Invoice Test Template",
        "template_id": "clean_business",  # This should match an existing template file
        "template_display_name": "Clean Business",
        "company_name": "Invoice Test Company",
        "address_line_1": "123 Invoice Street",
        "city": "Test City",
        "state_province": "Test State",
        "postal_code": "12345",
        "country": "Test Country",
        "phone": "******-0123",
        "email": "<EMAIL>",
        "website": "https://invoicecompany.com"
    }
    
    response = requests.post(f"{BASE_URL}/company-templates/", 
                           json=template_data, headers=headers)
    
    if response.status_code != 201:
        print(f"✗ Failed to create company template: {response.status_code}")
        print(response.text)
        return False
    
    company_template = response.json()
    print(f"✓ Company template created: {company_template['template_name']} (ID: {company_template['id']})")
    print(f"  Template ID: {company_template['template_id']}")
    
    # Step 3: Prepare test CSV data and column mappings
    print("\n📄 Step 3: Preparing Test Data")
    
    # Sample CSV data (as it would come from the frontend)
    csv_data = [
        {
            "Invoice ID": "INV-001",
            "Bill Date": "2024-01-15",
            "Client Company": "Test Client Corp",
            "Contact Email": "<EMAIL>",
            "Service Description": "Web Development Services",
            "Qty": "10",
            "Unit Rate": "100.00",
            "Line Amount": "1000.00"
        },
        {
            "Invoice ID": "INV-002", 
            "Bill Date": "2024-01-16",
            "Client Company": "Another Client LLC",
            "Contact Email": "<EMAIL>",
            "Service Description": "Consulting Services",
            "Qty": "5",
            "Unit Rate": "150.00",
            "Line Amount": "750.00"
        }
    ]
    
    # Column mappings (as they would come from the frontend)
    column_mappings = {
        "invoice_number": "Invoice ID",
        "bill_date": "Bill Date",
        "client_company": "Client Company",
        "contact_email": "Contact Email",
        "service_description": "Service Description",
        "quantity": "Qty",
        "unit_rate": "Unit Rate",
        "line_amount": "Line Amount"
    }
    
    print(f"✓ Prepared {len(csv_data)} rows of test data")
    print(f"✓ Prepared {len(column_mappings)} column mappings")
    
    # Step 4: Test invoice generation
    print("\n🎯 Step 4: Testing Invoice Generation")
    
    # Prepare the request data exactly as the frontend would send it
    request_data = {
        "csv_data": csv_data,
        "column_mappings": column_mappings,
        "company_template_id": company_template['id'],
        "template_id": company_template['template_id']  # This should fix the issue
    }
    
    print(f"Request data prepared:")
    print(f"  - CSV data rows: {len(request_data['csv_data'])}")
    print(f"  - Column mappings: {len(request_data['column_mappings'])}")
    print(f"  - Company template ID: {request_data['company_template_id']}")
    print(f"  - Template ID: {request_data['template_id']}")
    
    response = requests.post(f"{BASE_URL}/generate-invoices/", 
                           json=request_data, headers=headers)
    
    print(f"\nResponse status: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print("✅ Invoice generation SUCCESSFUL!")
            print(f"  - Generated {result['total_generated']} invoices")
            print(f"  - ZIP download URL: {result['zip_download_url']}")
            
            # Print details of generated invoices
            for i, invoice in enumerate(result['generated_invoices']):
                print(f"  Invoice {i+1}:")
                print(f"    - Client: {invoice['client_name']}")
                print(f"    - Invoice #: {invoice['invoice_number']}")
                print(f"    - Filename: {invoice['filename']}")
                print(f"    - Download URL: {invoice['download_url']}")
            
            return True
        else:
            print(f"✗ Invoice generation failed: {result.get('error', 'Unknown error')}")
            return False
    else:
        print(f"✗ Invoice generation request failed: {response.status_code}")
        try:
            error_data = response.json()
            print(f"Error details: {error_data}")
        except:
            print(f"Error response: {response.text}")
        return False

def test_template_id_fallback():
    """Test the template_id fallback mechanism."""
    print("\n🔄 Testing Template ID Fallback")
    print("-" * 30)
    
    user = create_test_user()
    token = get_auth_token()
    if not token:
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Get an existing company template
    response = requests.get(f"{BASE_URL}/company-templates/", headers=headers)
    if response.status_code != 200:
        print("✗ Failed to get company templates")
        return False
    
    templates = response.json()["results"]
    if not templates:
        print("✗ No company templates found")
        return False
    
    company_template = templates[0]
    print(f"Using existing template: {company_template['template_name']}")
    
    # Test with missing template_id in request (should fallback to company_template.template_id)
    request_data = {
        "csv_data": [{"Invoice ID": "INV-TEST", "Client Company": "Test Client", "Line Amount": "100.00"}],
        "column_mappings": {"invoice_number": "Invoice ID", "client_company": "Client Company", "line_amount": "Line Amount"},
        "company_template_id": company_template['id']
        # Note: template_id is intentionally missing to test fallback
    }
    
    response = requests.post(f"{BASE_URL}/generate-invoices/", 
                           json=request_data, headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print("✅ Template ID fallback works correctly!")
            return True
        else:
            print(f"✗ Fallback failed: {result.get('error')}")
            return False
    else:
        print(f"✗ Fallback test failed: {response.status_code}")
        try:
            error_data = response.json()
            print(f"Error details: {error_data}")
        except:
            print(f"Error response: {response.text}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Invoice Generation Fix Tests")
    print("=" * 50)
    
    # Test main invoice generation
    generation_passed = test_invoice_generation()
    
    # Test template_id fallback
    fallback_passed = test_template_id_fallback()
    
    print("\n" + "=" * 50)
    print("📋 TEST RESULTS")
    print("=" * 50)
    print(f"Invoice Generation: {'✅ PASSED' if generation_passed else '❌ FAILED'}")
    print(f"Template ID Fallback: {'✅ PASSED' if fallback_passed else '❌ FAILED'}")
    
    if generation_passed and fallback_passed:
        print("\n🎉 ALL TESTS PASSED! The template_id issue has been fixed.")
        print("\nFixed issues:")
        print("✅ Template ID is properly extracted from request")
        print("✅ Fallback to company_template.template_id works")
        print("✅ Better error messages with available templates")
        print("✅ Improved logging for debugging")
    else:
        print("\n❌ Some tests failed. Please check the output above.")
        sys.exit(1)
